import 'package:flutter/material.dart';
import 'package:get/get.dart';

// MODEL
class NavItem {
  final String id;
  final String label;
  final IconData icon;
  final List<NavItem>? children;
  final Color? color;

  NavItem({
    required this.id,
    required this.label,
    required this.icon,
    this.children,
    this.color,
  });
}

// GETX CONTROLLER
class AdminController extends GetxController {
  final RxString _activeItemId = 'dashboard'.obs;
  final RxString _activeLabel = 'Dashboard'.obs;
  final RxBool _isDrawerOpen = false.obs;

  String get activeItemId => _activeItemId.value;
  String get activeLabel => _activeLabel.value;
  bool get isDrawerOpen => _isDrawerOpen.value;

  void setActiveItem(String id, String label) {
    _activeItemId.value = id;
    _activeLabel.value = label;
  }

  void toggleDrawer() {
    _isDrawerOpen.value = !_isDrawerOpen.value;
  }
}

// VIEW
class AdminScaffold extends StatelessWidget {
  final List<NavItem> navItems;
  final Widget child;
  final AdminController controller;
  final Widget? header;
  final Widget? footer;

  const AdminScaffold({
    Key? key,
    required this.navItems,
    required this.child,
    required this.controller,
    this.header,
    this.footer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isDesktop = constraints.maxWidth >= 1200;
        final isTablet = constraints.maxWidth >= 600 && constraints.maxWidth < 1200;
        final isMobile = constraints.maxWidth < 600;

        final flatItems = _getFlatItems(navItems);
        final showBottomNav = isMobile && flatItems.length <= 5;

        return Scaffold(
          appBar: _buildAppBar(context, isDesktop, isTablet, isMobile),
          drawer: _buildDrawer(context, isDesktop, isTablet, isMobile),
          body: Row(
            children: [
              if (isDesktop) _buildDesktopSidebar(context),
              Expanded(
                child: Column(
                  children: [
                    if (header != null) header!,
                    Expanded(
                      child: Container(
                        color: Theme.of(context).colorScheme.surfaceContainerLowest,
                        child: child,
                      ),
                    ),
                    if (footer != null && !showBottomNav) footer!,
                  ],
                ),
              ),
            ],
          ),
          bottomNavigationBar: showBottomNav ? _buildBottomNav(context) : null,
        );
      },
    );
  }

  List<NavItem> _getFlatItems(List<NavItem> items) {
    List<NavItem> flat = [];
    for (var item in items) {
      if (item.children == null || item.children!.isEmpty) {
        flat.add(item);
      } else {
        flat.addAll(item.children!);
      }
    }
    return flat;
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    bool isDesktop,
    bool isTablet,
    bool isMobile,
  ) {
    return AppBar(
      elevation: 0,
      scrolledUnderElevation: 1,
      backgroundColor: Theme.of(context).colorScheme.surface,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      leading: !isDesktop
          ? IconButton(
              icon: const Icon(Icons.menu_rounded),
              onPressed: () => Scaffold.of(context).openDrawer(),
            )
          : null,
      title: Obx(() => Row(
            children: [
              if (isDesktop) ...[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.tertiary,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.admin_panel_settings_rounded,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
              ],
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    controller.activeLabel,
                    style: TextStyle(
                      fontSize: isDesktop ? 20 : 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (isDesktop)
                    Text(
                      'Admin Dashboard',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurfaceVariant,
                          ),
                    ),
                ],
              ),
            ],
          )),
      actions: [
        IconButton(
          icon: Badge(
            label: const Text('3'),
            child: const Icon(Icons.notifications_outlined),
          ),
          onPressed: () {},
        ),
        const SizedBox(width: 8),
        Padding(
          padding: const EdgeInsets.only(right: 16),
          child: CircleAvatar(
            radius: 18,
            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            child: Icon(
              Icons.person,
              size: 20,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopSidebar(BuildContext context) {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
          ),
        ),
      ),
      child: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(12),
              children: _buildNavItems(context, true, false, false),
            ),
          ),
          _buildSidebarFooter(context),
        ],
      ),
    );
  }

  Widget _buildSidebarFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
          ),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            child: Icon(
              Icons.person,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Admin User',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Text(
                  '<EMAIL>',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.logout_rounded, size: 20),
            onPressed: () {},
            tooltip: 'Logout',
          ),
        ],
      ),
    );
  }

  Widget? _buildDrawer(
    BuildContext context,
    bool isDesktop,
    bool isTablet,
    bool isMobile,
  ) {
    if (isDesktop) return null;

    final flatItems = _getFlatItems(navItems);
    final showBottomNav = isMobile && flatItems.length <= 5;
    
    if (showBottomNav) return null;

    return Drawer(
      width: isTablet ? 280 : null,
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        child: Column(
          children: [
            _buildDrawerHeader(context, isTablet),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(12),
                children: _buildNavItems(context, false, isTablet, isMobile),
              ),
            ),
            if (!isMobile) _buildSidebarFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context, bool isTablet) {
    return Container(
      padding: EdgeInsets.fromLTRB(24, MediaQuery.of(context).padding.top + 24, 24, 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.tertiary,
          ],
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.admin_panel_settings_rounded,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Admin Panel',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Management Console',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNav(BuildContext context) {
    final flatItems = _getFlatItems(navItems).take(5).toList();

    return Obx(() => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: flatItems.map((item) {
                  final isActive = controller.activeItemId == item.id;
                  return Expanded(
                    child: InkWell(
                      onTap: () => controller.setActiveItem(item.id, item.label),
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: isActive
                              ? Theme.of(context)
                                  .colorScheme
                                  .primaryContainer
                                  .withOpacity(0.5)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              item.icon,
                              color: isActive
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              size: 24,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              item.label,
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                    color: isActive
                                        ? Theme.of(context).colorScheme.primary
                                        : Theme.of(context).colorScheme.onSurfaceVariant,
                                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                                  ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ));
  }

  List<Widget> _buildNavItems(
    BuildContext context,
    bool isDesktop,
    bool isTablet,
    bool isMobile,
  ) {
    return navItems.map((item) {
      if (item.children != null && item.children!.isNotEmpty) {
        if (isMobile) {
          return _buildParentNavItem(context, item, isDesktop, isTablet, isMobile);
        }
        return _buildExpandableItem(context, item, isDesktop, isTablet, isMobile);
      }
      return _buildNavItem(context, item, isDesktop, isTablet, isMobile);
    }).toList();
  }

  Widget _buildParentNavItem(
    BuildContext context,
    NavItem item,
    bool isDesktop,
    bool isTablet,
    bool isMobile,
  ) {
    return Obx(() {
      final hasActiveChild = item.children!.any((child) => controller.activeItemId == child.id);
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Material(
          color: hasActiveChild
              ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            onTap: () {
              Navigator.pop(context);
              _showChildrenBottomSheet(context, item);
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    color: hasActiveChild
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      item.label,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: hasActiveChild ? FontWeight.w600 : FontWeight.w500,
                            color: hasActiveChild
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  void _showChildrenBottomSheet(BuildContext context, NavItem parentItem) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.4),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
              child: Row(
                children: [
                  Icon(
                    parentItem.icon,
                    color: Theme.of(context).colorScheme.primary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    parentItem.label,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Flexible(
              child: ListView(
                shrinkWrap: true,
                padding: const EdgeInsets.all(16),
                children: parentItem.children!.map((child) {
                  return Obx(() {
                    final isActive = controller.activeItemId == child.id;
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Material(
                        color: isActive
                            ? Theme.of(context).colorScheme.primaryContainer
                            : Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          onTap: () {
                            controller.setActiveItem(child.id, child.label);
                            Navigator.pop(context);
                          },
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Icon(
                                  child.icon,
                                  color: isActive
                                      ? Theme.of(context).colorScheme.onPrimaryContainer
                                      : Theme.of(context).colorScheme.onSurface,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    child.label,
                                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                          fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                                          color: isActive
                                              ? Theme.of(context).colorScheme.onPrimaryContainer
                                              : Theme.of(context).colorScheme.onSurface,
                                        ),
                                  ),
                                ),
                                if (isActive)
                                  Icon(
                                    Icons.check_circle,
                                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  });
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    NavItem item,
    bool isDesktop,
    bool isTablet,
    bool isMobile, {
    bool isChild = false,
  }) {
    return Obx(() {
      final isActive = controller.activeItemId == item.id;

      return Padding(
        padding: EdgeInsets.only(bottom: 4, left: isChild ? 12 : 0),
        child: Material(
          color: isActive
              ? Theme.of(context).colorScheme.primaryContainer
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            onTap: () {
              controller.setActiveItem(item.id, item.label);
              if (isMobile || isTablet) Navigator.pop(context);
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    color: isActive
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      item.label,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                            color: isActive
                                ? Theme.of(context).colorScheme.onPrimaryContainer
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildExpandableItem(
    BuildContext context,
    NavItem item,
    bool isDesktop,
    bool isTablet,
    bool isMobile,
  ) {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        leading: Icon(
          item.icon,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        title: Text(
          item.label,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
        ),
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        childrenPadding: const EdgeInsets.only(top: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        children: item.children!
            .map((child) => _buildNavItem(
                  context,
                  child,
                  isDesktop,
                  isTablet,
                  isMobile,
                  isChild: true,
                ))
            .toList(),
      ),
    );
  }
}

// USAGE EXAMPLE
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Admin Dashboard',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.light,
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      home: const AdminDashboard(),
    );
  }
}

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AdminController controller = Get.put(AdminController());

    final List<NavItem> navItems = [
      NavItem(
        id: 'dashboard',
        label: 'Dashboard',
        icon: Icons.dashboard_rounded,
      ),
      NavItem(
        id: 'users',
        label: 'Users',
        icon: Icons.people_rounded,
        children: [
          NavItem(id: 'all_users', label: 'All Users', icon: Icons.list_rounded),
          NavItem(id: 'add_user', label: 'Add User', icon: Icons.person_add_rounded),
          NavItem(id: 'roles', label: 'Roles & Permissions', icon: Icons.security_rounded),
        ],
      ),
      NavItem(
        id: 'products',
        label: 'Products',
        icon: Icons.inventory_2_rounded,
        children: [
          NavItem(id: 'all_products', label: 'All Products', icon: Icons.list_rounded),
          NavItem(id: 'add_product', label: 'Add Product', icon: Icons.add_box_rounded),
          NavItem(id: 'categories', label: 'Categories', icon: Icons.category_rounded),
        ],
      ),
      NavItem(
        id: 'orders',
        label: 'Orders',
        icon: Icons.shopping_cart_rounded,
      ),
      NavItem(
        id: 'analytics',
        label: 'Analytics',
        icon: Icons.analytics_rounded,
      ),
      NavItem(
        id: 'settings',
        label: 'Settings',
        icon: Icons.settings_rounded,
      ),
    ];

    return AdminScaffold(
      controller: controller,
      navItems: navItems,
      header: _buildHeader(context),
      footer: _buildFooter(context),
      child: Obx(() => _buildContent(context, controller)),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Welcome to your admin dashboard',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '© 2025 Admin Panel. All rights reserved.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, AdminController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primaryContainer,
                  Theme.of(context).colorScheme.tertiaryContainer,
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle_rounded,
              size: 80,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            controller.activeLabel,
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Text(
            'Page ID: ${controller.activeItemId}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }
}
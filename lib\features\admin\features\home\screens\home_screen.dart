import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/features/home/<USER>/widgets/admin_scaffold.dart';
import 'package:onekitty_admin/features/admin/widgets/kitty_dashboard.dart';
import 'package:onekitty_admin/features/admin/widgets/events_dashboard.dart';

class AdminHomeScreen extends StatefulWidget {
  const AdminHomeScreen({super.key});

  @override
  State<AdminHomeScreen> createState() => _AdminHomeScreenState();
}

class _AdminHomeScreenState extends State<AdminHomeScreen> {
  final AdminController _controller = AdminController();
  @override
  Widget build(BuildContext context) {
    return AdminScaffold(
      navItems: [
        NavItem(
          icon: Icons.dashboard_rounded,
          id: 'dashboard',
          label: 'Dashboard',
        ),
        NavItem(
          icon: Icons.savings_rounded,
          id: 'kitties',
          label: 'Kitties',
          children: [
            NavItem(icon: Icons.list_rounded, id: 'all_kitties', label: 'All Kitties'),
            NavItem(icon: Icons.analytics_rounded, id: 'kitty_analytics', label: 'Analytics'),
            NavItem(icon: Icons.report_rounded, id: 'kitty_reports', label: 'Reports'),
          ],
        ),
        NavItem(
          icon: Icons.event_rounded,
          id: 'events',
          label: 'Events',
          children: [
            NavItem(icon: Icons.list_rounded, id: 'all_events', label: 'All Events'),
            NavItem(icon: Icons.analytics_rounded, id: 'event_analytics', label: 'Analytics'),
            NavItem(icon: Icons.block_rounded, id: 'blocked_events', label: 'Blocked Events'),
          ],
        ),
        NavItem(
          icon: Icons.people_rounded,
          id: 'users',
          label: 'Users',
          children: [
            NavItem(icon: Icons.list_rounded, id: 'all_users', label: 'All Users'),
            NavItem(icon: Icons.person_add_rounded, id: 'add_user', label: 'Add User'),
            NavItem(icon: Icons.security_rounded, id: 'user_roles', label: 'Roles & Permissions'),
          ],
        ),
        NavItem(
          icon: Icons.analytics_rounded,
          id: 'analytics',
          label: 'Analytics',
        ),
        NavItem(
          icon: Icons.settings_rounded,
          id: 'settings',
          label: 'Settings',
        ),
      ],
      controller: _controller,
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Obx(() {
      switch (_controller.activeItemId) {
        case 'dashboard':
        case 'all_kitties':
          return const KittyDashboard();
        case 'kitty_analytics':
          return _buildPlaceholder('Kitty Analytics', Icons.analytics_rounded);
        case 'kitty_reports':
          return _buildPlaceholder('Kitty Reports', Icons.report_rounded);
        case 'all_events':
        case 'events':
          return const EventsDashboard();
        case 'event_analytics':
          return _buildPlaceholder('Event Analytics', Icons.analytics_rounded);
        case 'blocked_events':
          return _buildPlaceholder('Blocked Events', Icons.block_rounded);
        case 'all_users':
          return _buildPlaceholder('All Users', Icons.people_rounded);
        case 'add_user':
          return _buildPlaceholder('Add User', Icons.person_add_rounded);
        case 'user_roles':
          return _buildPlaceholder('User Roles', Icons.security_rounded);
        case 'analytics':
          return _buildPlaceholder('Analytics', Icons.analytics_rounded);
        case 'settings':
          return _buildPlaceholder('Settings', Icons.settings_rounded);
        default:
          return const KittyDashboard();
      }
    });
  }

  Widget _buildPlaceholder(String title, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primaryContainer,
                  Theme.of(context).colorScheme.tertiaryContainer,
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 80,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Text(
            'Coming Soon',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }
}

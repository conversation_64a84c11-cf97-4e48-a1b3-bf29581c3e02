import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:onekitty_admin/utils/confetti/confentti_widget.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';

class SucessPage extends StatefulWidget {
  final String? text;
  final String? kittyName;
  final SocialMesages? socialMessages;
  const SucessPage({super.key, this.text, this.kittyName, this.socialMessages});

  @override
  State<SucessPage> createState() => _SucessPageState();
}

class _SucessPageState extends State<SucessPage> {
  late ConfettiController _controllerCenter;
  List<String> socials = [
    "assets/images/insta.png",
    "assets/images/whatsapp.jpg",
    "assets/images/twitter.png",
    "assets/images/solar_link-bold.png",
    "assets/images/solar_link-bold.png",
  ];
  final KittyController kittyController = Get.put(KittyController());
  @override
  void initState() {
    super.initState();
    _controllerCenter =
        ConfettiController(duration: const Duration(seconds: 10));
    _controllerCenter.play();
  }

  @override
  void dispose() {
    _controllerCenter.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () {
                  // Navigate to the bottom nav section which is the home screen
                  Get.offAllNamed(NavRoutes.bottomNavSection);
                },
                icon: const Icon(Icons.arrow_back),
              ),
              Text("back".tr)
            ],
          ),
          SizedBox(
            height: SizeConfig.screenHeight * 0.15,
          ),
          Column(
            children: [
              ConfettiCustom(controllerCenter: _controllerCenter),
              Image.asset("assets/images/Vector.png"),
              const SizedBox(
                height: 30,
              ),
              RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(children: [
                    TextSpan(
                      text: widget.kittyName,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    TextSpan(
                      text: "${"was_successfully".tr}${widget.text}",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontSize: 18),
                    )
                  ])),
              const SizedBox(
                height: 15,
              ),
              Text(
                "share_kitty_link_message".tr,
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          await Share.share(
                              kittyController.socials["instagram"].toString());
                        },
                        child: Image.asset("assets/images/insta.png")),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          await Share.share(
                              kittyController.socials["tiktok"].toString());
                        },
                        child: SvgPicture.asset(
                            "assets/images/tiktok-outline.svg",
                            colorFilter: const ColorFilter.mode(
                                AppColors.primary, BlendMode.srcIn))),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          final Uri url = Uri.parse(
                              "https://twitter.com/intent/tweet?text=${kittyController.socials["twitter"].toString()}");
                          if (!await launchUrl(url)) {
                            ToastUtils.showErrorToast(
                                context, "could_not_launch_url".tr, "error".tr);
                          }
                        },
                        child: Image.asset("assets/images/twitter.png")),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          final Uri url = Uri.parse(
                              "https://www.facebook.com/sharer/sharer.php?quote=${kittyController.socials["facebook"]}");
                          if (!await launchUrl(url)) {
                            ToastUtils.showErrorToast(
                                context, "could_not_launch_url".tr, "error".tr);
                          }
                        },
                        child: Image.asset(
                          "assets/images/Vector (7).png",
                          //color: AppColors.blueButtonColor
                        )),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          await Share.share(
                              kittyController.socials["youtube"].toString());
                        },
                        child: Image.asset(
                          "assets/images/Vector (6).png",
                          //color: AppColors.blueButtonColor,
                        )),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              ElevatedButton(
                onPressed: () {
                  // Navigate to the bottom nav section which is the home screen
                  Get.offAllNamed(NavRoutes.bottomNavSection);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  "go_back_home".tr,
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}

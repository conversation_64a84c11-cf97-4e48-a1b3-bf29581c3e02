import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/features/home/<USER>/home_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/edit_event_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/view_single_event_viewer.dart';

class EventLoadingScreen extends StatefulWidget {
  const EventLoadingScreen({
    super.key,
  });

  @override
  State<EventLoadingScreen> createState() => _EventLoadingScreenState();
}

class _EventLoadingScreenState extends State<EventLoadingScreen> {
  final controller = Get.put(EditEventController());

  @override
  void initState() {
    super.initState();
  }

  Future<void> _loadEventData() async {
    try {
      var parameters = Get.parameters;
      String username = parameters['username'] ?? '';
      print('Loading event data for username: $username');
      final res = await controller.fetchEventDetailbyUsername(username);

      if (res) {
        // Ensure event data is properly loaded before navigating
        if (controller.event.value.id != 0) {
          // Navigate to the event viewer with the loaded data
          Get.offAll(
            () => ViewSingleEventViewer(
                isFromDeepLink: true, event: controller.event.value),
            transition: Transition.fadeIn,
          );
        } else {
          print('Event ID is invalid: ${controller.event.value.id}');
          Snack.showInfo(message1: 'Invalid event data');
          Get.offAll(() => AdminHomeScreen());
        }
      } else {
        // Handle error case
        print('Event not found for username: $username');
        Snack.showInfo(message1: 'Event not found or unavailable');
        Get.offAll(() => AdminHomeScreen());
      }
    } catch (e) {
      print('Error loading event: $e');
      Snack.showInfo(message1: 'Error loading event');
      Get.offAll(() => AdminHomeScreen());
    } finally {
      EasyLoading.dismiss(); // Dismiss loading before returning
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      splitScreenMode: true,
      designSize: const Size(392.72727272727275, 850.9090909090909),
      builder: (context, child) {
        return FutureBuilder(
          future: _loadEventData(),
          builder: (context, snapshot) {
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      color: AppColors.primary,
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      'loading_event'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/services/chamas_admin_service.dart';
import 'package:onekitty_admin/models/chama/chama_model.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';

class ChamasDashboardController extends GetxController {
  final ChamasAdminService _chamasService = ChamasAdminService();
  final Logger _logger = Logger();

  // Reactive variables
  final RxList<Chama> chamas = <Chama>[].obs;
  final RxBool isLoading = false.obs;
  final RxString apiMessage = ''.obs;
  final RxBool hasError = false.obs;

  // Pagination
  final RxInt currentPage = 0.obs;
  final RxInt pageSize = 15.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalItems = 0.obs;
  final RxBool hasNext = false.obs;
  final RxBool hasPrevious = false.obs;

  // Filters
  final RxString search = ''.obs;
  final RxString chamaId = ''.obs;
  final RxString frequency = ''.obs;
  final RxString kittyId = ''.obs;
  final RxString startDate = ''.obs;
  final RxString endDate = ''.obs;

  // PlutoGrid
  final RxList<PlutoRow> plutoRows = <PlutoRow>[].obs;
  final RxList<PlutoColumn> plutoColumns = <PlutoColumn>[].obs;

  Timer? _debounce;

  @override
  void onInit() {
    super.onInit();
    _initializeColumns();
    fetchChamas(0);
  }

  @override
  void onClose() {
    _debounce?.cancel();
    super.onClose();
  }

  void _initializeColumns() {
    plutoColumns.value = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableSorting: true,
        enableColumnDrag: false,
        frozen: PlutoColumnFrozen.start,
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Username',
        field: 'username',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Kitty ID',
        field: 'kitty_id',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Email',
        field: 'email',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Phone',
        field: 'phone',
        type: PlutoColumnType.text(),
        width: 130,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Amount',
        field: 'amount',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Balance',
        field: 'balance',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Next Occurrence',
        field: 'next_occurrence',
        type: PlutoColumnType.text(),
        width: 140,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: true,
      ),
    ];
  }

  Future<void> fetchChamas(int page) async {
    try {
      isLoading(true);
      hasError(false);
      
      final result = await _chamasService.fetchChamas(
        page: page,
        size: pageSize.value,
        search: search.value.isNotEmpty ? search.value : null,
        chamaId: chamaId.value.isNotEmpty ? chamaId.value : null,
        frequency: frequency.value.isNotEmpty ? frequency.value : null,
        kittyId: kittyId.value.isNotEmpty ? kittyId.value : null,
        startDate: startDate.value.isNotEmpty ? startDate.value : null,
        endDate: endDate.value.isNotEmpty ? endDate.value : null,
      );

      if (result['success']) {
        chamas.value = result['chamas'];
        _updatePlutoRows();
        
        final pagination = result['pagination'];
        currentPage.value = pagination['page'];
        totalPages.value = pagination['total_pages'];
        totalItems.value = pagination['total_items'];
        hasNext.value = pagination['has_next'];
        hasPrevious.value = pagination['has_previous'];
        
        apiMessage.value = result['message'];
      } else {
        hasError(true);
        apiMessage.value = result['message'];
        chamas.clear();
        plutoRows.clear();
      }
    } catch (e) {
      _logger.e('Error in fetchChamas: $e');
      hasError(true);
      apiMessage.value = 'An unexpected error occurred';
      chamas.clear();
      plutoRows.clear();
    } finally {
      isLoading(false);
    }
  }

  void _updatePlutoRows() {
    plutoRows.value = chamas.map((chama) {
      return PlutoRow(cells: {
        'id': PlutoCell(value: chama.id?.toString() ?? '-'),
        'created_at': PlutoCell(
          value: chama.createdAt != null
              ? DateFormat('dd/MM/yy HH:mm').format(chama.createdAt!)
              : '-',
        ),
        'title': PlutoCell(value: chama.title ?? '-'),
        'username': PlutoCell(value: chama.username ?? '-'),
        'kitty_id': PlutoCell(value: chama.kittyId?.toString() ?? '-'),
        'email': PlutoCell(value: chama.email ?? '-'),
        'phone': PlutoCell(value: chama.phoneNumber ?? '-'),
        'amount': PlutoCell(value: _formatCurrency(chama.amount)),
        'balance': PlutoCell(value: _formatCurrency(chama.balance)),
        'next_occurrence': PlutoCell(
          value: chama.nextOccurrence != null
              ? DateFormat('dd/MM/yy HH:mm').format(chama.nextOccurrence!)
              : '-',
        ),
        'status': PlutoCell(value: _getStatusDisplayText(chama.status)),
      });
    }).toList();
  }

  String _formatCurrency(dynamic amount) {
    if (amount == null) return 'KES 0.00';
    
    double value = 0.0;
    if (amount is String) {
      value = double.tryParse(amount) ?? 0.0;
    } else if (amount is num) {
      value = amount.toDouble();
    }
    
    return 'KES ${NumberFormat('#,##0.00').format(value)}';
  }

  String _getStatusDisplayText(String? status) {
    if (status == null) return '-';
    return status.toUpperCase();
  }

  Color getChamaStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF56AF57);
      case "completed":
        return const Color(0xFF56AF57);
      case "suspended":
        return const Color(0xFFEE5B60);
      case "pending":
        return Colors.amber;
      default:
        return const Color(0xFFEE5B60);
    }
  }

  void handleSearch(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    
    _debounce = Timer(const Duration(seconds: 1), () {
      search.value = value;
      fetchChamas(0);
    });
  }

  void handlePageChange(int page) {
    fetchChamas(page);
  }

  void clearFilters() {
    search.value = '';
    chamaId.value = '';
    frequency.value = '';
    kittyId.value = '';
    startDate.value = '';
    endDate.value = '';
    fetchChamas(0);
  }

  void refreshData() {
    fetchChamas(currentPage.value);
  }

  Future<void> selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      startDate.value = picked.toUtc().toIso8601String();
      fetchChamas(0);
    }
  }

  Future<void> selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      endDate.value = picked.toUtc().toIso8601String();
      fetchChamas(0);
    }
  }

  void clearStartDate() {
    startDate.value = '';
    fetchChamas(0);
  }

  void clearEndDate() {
    endDate.value = '';
    fetchChamas(0);
  }

  void onChamaTapped(PlutoRow row) {
    final index = plutoRows.indexOf(row);
    if (index >= 0 && index < chamas.length) {
      final selectedChama = chamas[index];
      _logger.i('Chama tapped: ${selectedChama.title}');
      
      // Navigate to single chama view
      Get.snackbar(
        'Chama Selected',
        'Selected: ${selectedChama.title}',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  Future<void> sendFundsToBeneficiaries(int chamaId) async {
    try {
      isLoading(true);
      
      final result = await _chamasService.sendFundsToBeneficiaries(
        chamaId: chamaId,
      );

      if (result['success']) {
        Get.snackbar(
          'Success',
          'Funds sent to beneficiaries successfully',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        refreshData();
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error sending funds to beneficiaries: $e');
      Get.snackbar(
        'Error',
        'An error occurred while sending funds',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> addGeneralPenalty({
    required int chamaId,
    required double amount,
    required String reason,
    String? description,
  }) async {
    try {
      isLoading(true);
      
      final result = await _chamasService.addGeneralPenalty(
        chamaId: chamaId,
        amount: amount,
        reason: reason,
        description: description,
      );

      if (result['success']) {
        Get.snackbar(
          'Success',
          'Penalty added successfully',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        refreshData();
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error adding penalty: $e');
      Get.snackbar(
        'Error',
        'An error occurred while adding penalty',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> blockChama(int chamaId, String reason) async {
    try {
      isLoading(true);
      
      final result = await _chamasService.blockChama(
        chamaId: chamaId,
        reason: reason,
      );

      if (result['success']) {
        Get.snackbar(
          'Success',
          'Chama blocked successfully',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        refreshData();
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error blocking chama: $e');
      Get.snackbar(
        'Error',
        'An error occurred while blocking chama',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> unblockChama(int chamaId) async {
    try {
      isLoading(true);
      
      final result = await _chamasService.unblockChama(chamaId);

      if (result['success']) {
        Get.snackbar(
          'Success',
          'Chama unblocked successfully',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        refreshData();
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error unblocking chama: $e');
      Get.snackbar(
        'Error',
        'An error occurred while unblocking chama',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> exportData(String format) async {
    try {
      isLoading(true);
      
      final filters = <String, dynamic>{
        'search': search.value,
        'chama_id': chamaId.value,
        'frequency': frequency.value,
        'kitty_id': kittyId.value,
        'start_date': startDate.value,
        'end_date': endDate.value,
      };

      final result = await _chamasService.exportChamasData(
        format: format,
        filters: filters,
      );

      if (result['success']) {
        Get.snackbar(
          'Export Successful',
          'Data exported successfully. Download will start shortly.',
          snackPosition: SnackPosition.bottom,
        );
      } else {
        Get.snackbar(
          'Export Failed',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error exporting data: $e');
      Get.snackbar(
        'Export Error',
        'An error occurred while exporting data',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }
}

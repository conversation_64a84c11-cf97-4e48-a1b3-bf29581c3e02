import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/events_dashboard_controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/my_pluto_grid.dart';
import 'package:onekitty_admin/features/admin/widgets/events_filter_widget.dart';

class EventsDashboard extends StatefulWidget {
  const EventsDashboard({super.key});

  @override
  State<EventsDashboard> createState() => _EventsDashboardState();
}

class _EventsDashboardState extends State<EventsDashboard> {
  late final EventsDashboardController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(EventsDashboardController());
  }

  @override
  void dispose() {
    Get.delete<EventsDashboardController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value && controller.events.isEmpty) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (controller.hasError.value && controller.events.isEmpty) {
                return _buildErrorState(context);
              }

              return _buildTable(context);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.event_rounded,
                size: 28,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 12),
              Text(
                'Events Dashboard',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const Spacer(),
              Obx(() => Text(
                    'Total: ${controller.totalItems.value} events',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                  )),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search events...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onChanged: controller.handleSearch,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog(context),
                tooltip: 'Filters',
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: controller.refreshData,
                tooltip: 'Refresh',
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.download),
                tooltip: 'Export',
                onSelected: controller.exportData,
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'csv', child: Text('Export as CSV')),
                  const PopupMenuItem(value: 'excel', child: Text('Export as Excel')),
                  const PopupMenuItem(value: 'pdf', child: Text('Export as PDF')),
                ],
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.admin_panel_settings),
                tooltip: 'Admin Actions',
                onSelected: (value) => _handleAdminAction(context, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'block_selected', child: Text('Block Selected')),
                  const PopupMenuItem(value: 'unblock_selected', child: Text('Unblock Selected')),
                  const PopupMenuItem(value: 'view_transactions', child: Text('View Transactions')),
                ],
              ),
            ],
          ),
          Obx(() {
            if (controller.apiMessage.value.isNotEmpty) {
              return Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: controller.hasError.value
                      ? Colors.red.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      controller.hasError.value
                          ? Icons.error_outline
                          : Icons.info_outline,
                      size: 16,
                      color: controller.hasError.value
                          ? Colors.red
                          : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        controller.apiMessage.value,
                        style: TextStyle(
                          color: controller.hasError.value
                              ? Colors.red
                              : Colors.green,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildTable(BuildContext context) {
    return Obx(() => AppTable(
          columns: controller.plutoColumns,
          rows: controller.plutoRows,
          title: null, // We have our own header
          totalPages: controller.totalPages.value,
          totalSizePerPage: controller.pageSize.value,
          onPageNavigated: controller.handlePageChange,
          onItemClicked: controller.onEventTapped,
          onRefresh: controller.refreshData,
          cacheKey: 'events_dashboard',
          enableSorting: true,
          enableExport: true,
          enableMultiSelect: false,
          emptyStateWidget: _buildEmptyState(context),
          loadingWidget: const Center(
            child: CircularProgressIndicator(),
          ),
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No events found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.clearFilters,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load events',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
                controller.apiMessage.value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[500],
                    ),
                textAlign: TextAlign.center,
              )),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.refreshData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          child: EventsFilterWidget(controller: controller),
        ),
      ),
    );
  }

  void _handleAdminAction(BuildContext context, String action) {
    switch (action) {
      case 'block_selected':
        _showBlockEventDialog(context);
        break;
      case 'unblock_selected':
        _showUnblockEventDialog(context);
        break;
      case 'view_transactions':
        Get.snackbar(
          'Coming Soon',
          'Transaction view feature will be available soon',
          snackPosition: SnackPosition.bottom,
        );
        break;
    }
  }

  void _showBlockEventDialog(BuildContext context) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Event'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for blocking this event:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                // For demo purposes, we'll block the first event
                if (controller.events.isNotEmpty) {
                  controller.blockEvent(
                    controller.events.first.id ?? 0,
                    reasonController.text,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Block Event'),
          ),
        ],
      ),
    );
  }

  void _showUnblockEventDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unblock Event'),
        content: const Text('Are you sure you want to unblock this event?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll unblock the first event
              if (controller.events.isNotEmpty) {
                controller.unblockEvent(controller.events.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Unblock Event'),
          ),
        ],
      ),
    );
  }
}

// ==================== app_table.dart ====================
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/model/action_button.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/widget.dart';
import 'package:pluto_grid/pluto_grid.dart';

class AppTable extends StatefulWidget {
  // Required parameters
  final List<PlutoColumn> columns;
  final List<PlutoRow> rows;
  final Function()? onInit;

  // Nullable parameters
  final int? totalPages;
  final int? totalSizePerPage;
  final Function(int page)? onPageNavigated;
  final String? title;
  final Function(String searchWord)? onSearch;
  final Function()? onRefresh;
  final Function(PlutoRow row)? onItemClicked;
  final Function(PlutoRow row, List<ActionButton> actions)? onItemLongPressed;
  final Function()? onFilterIconClicked;
  final List<Widget>? filterWidgets;
  final List<ActionButton>? actions;
  final String? cacheKey;
  final bool? enableSorting;
  final bool? enableExport;
  final bool? enableMultiSelect;
  final List<ActionButton>? bulkActions;
  final Function(List<PlutoRow> selectedRows)? onSelectionChanged;
  final Widget? emptyStateWidget;
  final Widget? loadingWidget;

  const AppTable({
    Key? key,
    required this.columns,
    required this.rows,
    this.onInit,
    this.totalPages,
    this.totalSizePerPage,
    this.onPageNavigated,
    this.title,
    this.onSearch,
    this.onRefresh,
    this.onItemClicked,
    this.onItemLongPressed,
    this.onFilterIconClicked,
    this.filterWidgets,
    this.actions,
    this.cacheKey,
    this.enableSorting,
    this.enableExport,
    this.enableMultiSelect,
    this.bulkActions,
    this.onSelectionChanged,
    this.emptyStateWidget,
    this.loadingWidget,
  }) : super(key: key);

  @override
  State<AppTable> createState() => _AppTableState();
}

class _AppTableState extends State<AppTable> with WidgetsBindingObserver {
  late final AppTableController controller;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    controller = Get.put(
      AppTableController(
        cacheKey: widget.cacheKey,
        onPageNavigated: widget.onPageNavigated,
        onSearch: widget.onSearch,
        onRefresh: widget.onRefresh,
        onSelectionChanged: widget.onSelectionChanged,
      ),
      tag: widget.cacheKey ?? DateTime.now().toString(),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onInit?.call();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    Get.delete<AppTableController>(tag: widget.cacheKey ?? DateTime.now().toString());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppTableView(
      controller: controller,
      columns: widget.columns,
      rows: widget.rows,
      title: widget.title,
      totalPages: widget.totalPages,
      totalSizePerPage: widget.totalSizePerPage,
      onItemClicked: widget.onItemClicked,
      onItemLongPressed: widget.onItemLongPressed,
      filterWidgets: widget.filterWidgets,
      actions: widget.actions,
      bulkActions: widget.bulkActions,
      emptyStateWidget: widget.emptyStateWidget,
      loadingWidget: widget.loadingWidget,
      enableSorting: widget.enableSorting ?? true,
      enableExport: widget.enableExport ?? false,
      enableMultiSelect: widget.enableMultiSelect ?? false,
    );
  }
}

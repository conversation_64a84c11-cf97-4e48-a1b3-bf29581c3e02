import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/api_urls.dart';
import 'package:onekitty_admin/models/chama/chama_model.dart';
import 'package:onekitty_admin/services/api_provider.dart';

class ChamasAdminService {
  final ApiProvider _apiProvider = ApiProvider();
  final Logger _logger = Logger();

  /// Fetch chamas with pagination and filtering
  Future<Map<String, dynamic>> fetchChamas({
    int page = 0,
    int size = 15,
    String? search,
    String? chamaId,
    String? frequency,
    String? kittyId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'size': size.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (chamaId != null && chamaId.isNotEmpty) {
        queryParams['chama_id'] = chamaId;
      }
      if (frequency != null && frequency.isNotEmpty) {
        queryParams['frequency'] = frequency;
      }
      if (kittyId != null && kittyId.isNotEmpty) {
        queryParams['kitty_id'] = kittyId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        queryParams['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParams['end_date'] = endDate;
      }

      final String queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final String url = '${AdminApiUrls.getAllChamasAdmin}?$queryString';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data['data'];
        final items = data['items'] as List? ?? [];
        
        final chamas = items.map((item) => Chama.fromJson(item)).toList();

        return {
          'success': true,
          'message': response.data['message'] ?? 'Chamas fetched successfully',
          'chamas': chamas,
          'pagination': {
            'page': data['page'] ?? page,
            'size': data['size'] ?? size,
            'total_pages': data['total_pages'] ?? 1,
            'total_items': data['total_items'] ?? chamas.length,
            'has_next': data['has_next'] ?? false,
            'has_previous': data['has_previous'] ?? false,
            'is_first': data['first'] ?? true,
            'is_last': data['last'] ?? true,
          },
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to fetch chamas',
          'chamas': <Chama>[],
          'pagination': {
            'page': page,
            'size': size,
            'total_pages': 1,
            'total_items': 0,
            'has_next': false,
            'has_previous': false,
            'is_first': true,
            'is_last': true,
          },
        };
      }
    } catch (e) {
      _logger.e('Error fetching chamas: $e');
      return {
        'success': false,
        'message': 'An error occurred while fetching chamas',
        'chamas': <Chama>[],
        'pagination': {
          'page': page,
          'size': size,
          'total_pages': 1,
          'total_items': 0,
          'has_next': false,
          'has_previous': false,
          'is_first': true,
          'is_last': true,
        },
      };
    }
  }

  /// Fetch single chama details
  Future<Map<String, dynamic>> fetchChamaById(int chamaId) async {
    try {
      final String url = '${AdminApiUrls.getChamaDetails}?chama_id=$chamaId';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Chama details fetched successfully',
          'chama': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to fetch chama details',
          'chama': null,
        };
      }
    } catch (e) {
      _logger.e('Error fetching chama details: $e');
      return {
        'success': false,
        'message': 'An error occurred while fetching chama details',
        'chama': null,
      };
    }
  }

  /// Send funds to beneficiaries
  Future<Map<String, dynamic>> sendFundsToBeneficiaries({
    required int chamaId,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.sendFundsToBeneficiaries,
        data: {'chama_id': chamaId},
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Funds sent successfully',
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to send funds',
          'data': null,
        };
      }
    } catch (e) {
      _logger.e('Error sending funds to beneficiaries: $e');
      return {
        'success': false,
        'message': 'An error occurred while sending funds',
        'data': null,
      };
    }
  }

  /// Add general penalty to chama
  Future<Map<String, dynamic>> addGeneralPenalty({
    required int chamaId,
    required double amount,
    required String reason,
    String? description,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.addGeneralPenalty,
        data: {
          'chama_id': chamaId,
          'amount': amount,
          'reason': reason,
          if (description != null) 'description': description,
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Penalty added successfully',
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to add penalty',
          'data': null,
        };
      }
    } catch (e) {
      _logger.e('Error adding general penalty: $e');
      return {
        'success': false,
        'message': 'An error occurred while adding penalty',
        'data': null,
      };
    }
  }

  /// Block chama
  Future<Map<String, dynamic>> blockChama({
    required int chamaId,
    required String reason,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.blockChama,
        data: {
          'chama_id': chamaId,
          'reason': reason,
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Chama blocked successfully',
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to block chama',
          'data': null,
        };
      }
    } catch (e) {
      _logger.e('Error blocking chama: $e');
      return {
        'success': false,
        'message': 'An error occurred while blocking chama',
        'data': null,
      };
    }
  }

  /// Unblock chama
  Future<Map<String, dynamic>> unblockChama(int chamaId) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.unblockChama,
        data: {'chama_id': chamaId},
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Chama unblocked successfully',
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to unblock chama',
          'data': null,
        };
      }
    } catch (e) {
      _logger.e('Error unblocking chama: $e');
      return {
        'success': false,
        'message': 'An error occurred while unblocking chama',
        'data': null,
      };
    }
  }

  /// Fetch chama transactions
  Future<Map<String, dynamic>> fetchChamaTransactions({
    required int chamaId,
    int page = 0,
    int size = 15,
    String? filters,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'size': size.toString(),
        'chama_id': chamaId.toString(),
      };

      if (filters != null && filters.isNotEmpty) {
        queryParams['filters'] = filters;
      }

      final String queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final String url = '${AdminApiUrls.getChamaTransactions}?$queryString';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Transactions fetched successfully',
          'transactions': response.data['data']['items'] ?? [],
          'pagination': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to fetch transactions',
          'transactions': [],
          'pagination': null,
        };
      }
    } catch (e) {
      _logger.e('Error fetching chama transactions: $e');
      return {
        'success': false,
        'message': 'An error occurred while fetching transactions',
        'transactions': [],
        'pagination': null,
      };
    }
  }

  /// Export chamas data
  Future<Map<String, dynamic>> exportChamasData({
    required String format,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'format': format,
        if (filters != null) ...filters,
      };

      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.exportChamasData,
        data: requestData,
      );

      if (response.statusCode == 200 && response.data != null) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Export completed successfully',
          'download_url': response.data['download_url'],
        };
      } else {
        return {
          'success': false,
          'message': response.data?['message'] ?? 'Failed to export data',
          'download_url': null,
        };
      }
    } catch (e) {
      _logger.e('Error exporting chamas data: $e');
      return {
        'success': false,
        'message': 'An error occurred while exporting data',
        'download_url': null,
      };
    }
  }
}

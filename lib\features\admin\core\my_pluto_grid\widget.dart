
// ==================== app_table_view.dart ====================
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/model/action_button.dart';
import 'package:pluto_grid/pluto_grid.dart';

class AppTableView extends StatelessWidget {
  final AppTableController controller;
  final List<PlutoColumn> columns;
  final List<PlutoRow> rows;
  final String? title;
  final int? totalPages;
  final int? totalSizePerPage;
  final Function(PlutoRow row)? onItemClicked;
  final Function(PlutoRow row, List<ActionButton> actions)? onItemLongPressed;
  final List<Widget>? filterWidgets;
  final List<ActionButton>? actions;
  final List<ActionButton>? bulkActions;
  final Widget? emptyStateWidget;
  final Widget? loadingWidget;
  final bool enableSorting;
  final bool enableExport;
  final bool enableMultiSelect;

  const AppTableView({
    Key? key,
    required this.controller,
    required this.columns,
    required this.rows,
    this.title,
    this.totalPages,
    this.totalSizePerPage,
    this.onItemClicked,
    this.onItemLongPressed,
    this.filterWidgets,
    this.actions,
    this.bulkActions,
    this.emptyStateWidget,
    this.loadingWidget,
    this.enableSorting = true,
    this.enableExport = false,
    this.enableMultiSelect = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: _buildTableBody(context),
              ),
              _buildFooter(context),
            ],
          ),
          _buildFilterDrawer(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Obx(() => AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: controller.showHeaderFooter.value ? null : 0,
      child: controller.showHeaderFooter.value
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null) ...[
                    Text(
                      title!,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 12),
                  ],
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: TextEditingController(
                            text: controller.searchQuery.value,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Search...',
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          onChanged: controller.handleSearch,
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (filterWidgets != null && filterWidgets!.isNotEmpty)
                        IconButton(
                          icon: const Icon(Icons.filter_list),
                          onPressed: controller.toggleFilterDrawer,
                          tooltip: 'Filters',
                        ),
                      if (controller.onRefresh != null)
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: controller.refreshTable,
                          tooltip: 'Refresh',
                        ),
                      if (enableExport)
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.download),
                          tooltip: 'Export',
                          onSelected: controller.exportData,
                          itemBuilder: (context) => [
                            const PopupMenuItem(value: 'csv', child: Text('Export as CSV')),
                            const PopupMenuItem(value: 'excel', child: Text('Export as Excel')),
                            const PopupMenuItem(value: 'pdf', child: Text('Export as PDF')),
                          ],
                        ),
                      if (actions != null) ...actions!.map((action) => 
                        IconButton(
                          icon: Icon(action.icon),
                          onPressed: action.onTap,
                          tooltip: action.label,
                        ),
                      ),
                    ],
                  ),
                  Obx(() {
                    if (controller.selectedRows.isNotEmpty && bulkActions != null) {
                      return Container(
                        margin: const EdgeInsets.only(top: 12),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Text(
                              '${controller.selectedRows.length} selected',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            ...bulkActions!.map((action) => 
                              TextButton.icon(
                                icon: Icon(action.icon, size: 18),
                                label: Text(action.label),
                                onPressed: () => _handleActionWithConfirmation(
                                  context, 
                                  action,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                ],
              ),
            )
          : const SizedBox.shrink(),
    ));
  }

  Widget _buildTableBody(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return loadingWidget ?? const Center(child: CircularProgressIndicator());
      }

      if (rows.isEmpty) {
        return emptyStateWidget ?? 
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.inbox, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No data available',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          );
      }

      return RefreshIndicator(
        onRefresh: () async {
          if (controller.onRefresh != null) {
            await controller.refreshTable();
          }
        },
        child: PlutoGrid(
          columns: columns,
          rows: rows,
          onLoaded: (PlutoGridOnLoadedEvent event) {
            controller.initStateManager(event.stateManager);
            event.stateManager.setShowColumnFilter(true);
          },
          onRowDoubleTap: (event) {
            onItemClicked?.call(event.row);
          },
          onRowSecondaryTap: (event) {
            if (onItemLongPressed != null) {
              _showContextMenu(context, event.row);
            }
          },
          configuration: PlutoGridConfiguration(
            style: PlutoGridStyleConfig(
              gridBackgroundColor: Theme.of(context).scaffoldBackgroundColor,
              rowColor: Theme.of(context).cardColor,
              activatedColor: Theme.of(context).primaryColor.withOpacity(0.1),
              checkedColor: Theme.of(context).primaryColor.withOpacity(0.2),
            ),
            columnSize: const PlutoGridColumnSizeConfig(
              autoSizeMode: PlutoAutoSizeMode.scale,
              resizeMode: PlutoResizeMode.pushAndPull,
            ),
            enableMoveDownAfterSelecting: true,
            enterKeyAction: PlutoGridEnterKeyAction.toggleEditing,
          ),
          mode: enableMultiSelect 
            ? PlutoGridMode.selectWithOneTap 
            : PlutoGridMode.normal,
        ),
      );
    });
  }

  Widget _buildFooter(BuildContext context) {
    if (totalPages == null || totalPages! <= 1) {
      return const SizedBox.shrink();
    }

    return Obx(() => AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: controller.showHeaderFooter.value ? null : 0,
      child: controller.showHeaderFooter.value
          ? Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.chevron_left),
                    onPressed: controller.currentPage.value > 1
                        ? () => controller.goToPage(controller.currentPage.value - 1)
                        : null,
                  ),
                  const SizedBox(width: 8),
                  ..._buildPageNumbers(),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.chevron_right),
                    onPressed: controller.currentPage.value < totalPages!
                        ? () => controller.goToPage(controller.currentPage.value + 1)
                        : null,
                  ),
                  if (totalSizePerPage != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: Text(
                        'Showing $totalSizePerPage items per page',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                ],
              ),
            )
          : const SizedBox.shrink(),
    ));
  }

  List<Widget> _buildPageNumbers() {
    final List<Widget> pageWidgets = [];
    final currentPage = controller.currentPage.value;
    final maxVisible = 5;

    int start = (currentPage - 2).clamp(1, totalPages!);
    int end = (start + maxVisible - 1).clamp(1, totalPages!);

    if (end - start < maxVisible - 1) {
      start = (end - maxVisible + 1).clamp(1, totalPages!);
    }

    for (int i = start; i <= end; i++) {
      pageWidgets.add(
        Obx(() => Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: InkWell(
            onTap: () => controller.goToPage(i),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: i == controller.currentPage.value
                    ? Get.theme.primaryColor
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: i == controller.currentPage.value
                      ? Get.theme.primaryColor
                      : Colors.grey.withOpacity(0.3),
                ),
              ),
              child: Text(
                '$i',
                style: TextStyle(
                  color: i == controller.currentPage.value
                      ? Colors.white
                      : Get.theme.textTheme.bodyMedium?.color,
                  fontWeight: i == controller.currentPage.value
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
        )),
      );
    }

    return pageWidgets;
  }

  Widget _buildFilterDrawer(BuildContext context) {
    if (filterWidgets == null || filterWidgets!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Obx(() => AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      right: controller.showFilterDrawer.value ? 0 : -300,
      top: 0,
      bottom: 0,
      width: 300,
      child: Material(
        elevation: 8,
        child: Container(
          color: Theme.of(context).cardColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Filters',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: controller.toggleFilterDrawer,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: filterWidgets!,
                ),
              ),
            ],
          ),
        ),
      ),
    ));
  }

  void _showContextMenu(BuildContext context, PlutoRow row) {
    // Get row position for menu placement
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            // Actions will be added via callback
            if (onItemLongPressed != null)
              FutureBuilder(
                future: Future.value(onItemLongPressed!(row, [])),
                builder: (context, snapshot) => const SizedBox.shrink(),
              ),
          ],
        ),
      ),
    );
  }

  void _handleActionWithConfirmation(BuildContext context, ActionButton action) {
    if (action.showMessage != null) {
      Get.dialog(
        AlertDialog(
          title: const Text('Confirm Action'),
          content: Text(action.showMessage!),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                action.onTap();
              },
              child: const Text('Confirm'),
            ),
          ],
        ),
      );
    } else {
      action.onTap();
    }
  }
}


import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/chamas_dashboard_controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/my_pluto_grid.dart';
import 'package:onekitty_admin/features/admin/widgets/chamas_filter_widget.dart';

class ChamasDashboard extends StatefulWidget {
  const ChamasDashboard({super.key});

  @override
  State<ChamasDashboard> createState() => _ChamasDashboardState();
}

class _ChamasDashboardState extends State<ChamasDashboard> {
  late final ChamasDashboardController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(ChamasDashboardController());
  }

  @override
  void dispose() {
    Get.delete<ChamasDashboardController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value && controller.chamas.isEmpty) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (controller.hasError.value && controller.chamas.isEmpty) {
                return _buildErrorState(context);
              }

              return _buildTable(context);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.savings_rounded,
                size: 28,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 12),
              Text(
                'Chamas Dashboard',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const Spacer(),
              Obx(() => Text(
                    'Total: ${controller.totalItems.value} chamas',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                  )),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search chamas...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onChanged: controller.handleSearch,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog(context),
                tooltip: 'Filters & Actions',
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: controller.refreshData,
                tooltip: 'Refresh',
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.download),
                tooltip: 'Export',
                onSelected: controller.exportData,
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'csv', child: Text('Export as CSV')),
                  const PopupMenuItem(value: 'excel', child: Text('Export as Excel')),
                  const PopupMenuItem(value: 'pdf', child: Text('Export as PDF')),
                ],
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.admin_panel_settings),
                tooltip: 'Admin Actions',
                onSelected: (value) => _handleAdminAction(context, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'send_funds', child: Text('Send Funds to Beneficiaries')),
                  const PopupMenuItem(value: 'add_penalty', child: Text('Add General Penalty')),
                  const PopupMenuItem(value: 'block_selected', child: Text('Block Selected Chama')),
                  const PopupMenuItem(value: 'unblock_selected', child: Text('Unblock Selected Chama')),
                  const PopupMenuItem(value: 'view_transactions', child: Text('View Transactions')),
                  const PopupMenuItem(value: 'view_settings', child: Text('View Settings')),
                ],
              ),
            ],
          ),
          Obx(() {
            if (controller.apiMessage.value.isNotEmpty) {
              return Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: controller.hasError.value
                      ? Colors.red.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      controller.hasError.value
                          ? Icons.error_outline
                          : Icons.info_outline,
                      size: 16,
                      color: controller.hasError.value
                          ? Colors.red
                          : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        controller.apiMessage.value,
                        style: TextStyle(
                          color: controller.hasError.value
                              ? Colors.red
                              : Colors.green,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildTable(BuildContext context) {
    return Obx(() => AppTable(
          columns: controller.plutoColumns,
          rows: controller.plutoRows,
          title: null, // We have our own header
          totalPages: controller.totalPages.value,
          totalSizePerPage: controller.pageSize.value,
          onPageNavigated: controller.handlePageChange,
          onItemClicked: controller.onChamaTapped,
          onRefresh: controller.refreshData,
          cacheKey: 'chamas_dashboard',
          enableSorting: true,
          enableExport: true,
          enableMultiSelect: false,
          emptyStateWidget: _buildEmptyState(context),
          loadingWidget: const Center(
            child: CircularProgressIndicator(),
          ),
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.savings_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No chamas found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.clearFilters,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load chamas',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
                controller.apiMessage.value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[500],
                    ),
                textAlign: TextAlign.center,
              )),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.refreshData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          padding: const EdgeInsets.all(24),
          child: SingleChildScrollView(
            child: ChamasFilterWidget(controller: controller),
          ),
        ),
      ),
    );
  }

  void _handleAdminAction(BuildContext context, String action) {
    switch (action) {
      case 'send_funds':
        _showSendFundsDialog(context);
        break;
      case 'add_penalty':
        _showAddPenaltyDialog(context);
        break;
      case 'block_selected':
        _showBlockChamaDialog(context);
        break;
      case 'unblock_selected':
        _showUnblockChamaDialog(context);
        break;
      case 'view_transactions':
        Get.snackbar(
          'Coming Soon',
          'Transaction view feature will be available soon',
          snackPosition: SnackPosition.bottom,
        );
        break;
      case 'view_settings':
        Get.snackbar(
          'Coming Soon',
          'Settings view feature will be available soon',
          snackPosition: SnackPosition.bottom,
        );
        break;
    }
  }

  void _showSendFundsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Funds to Beneficiaries'),
        content: const Text('Are you sure you want to send funds to the current beneficiaries?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll use the first chama
              if (controller.chamas.isNotEmpty) {
                controller.sendFundsToBeneficiaries(controller.chamas.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Send Funds'),
          ),
        ],
      ),
    );
  }

  void _showAddPenaltyDialog(BuildContext context) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();
    final descriptionController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add General Penalty'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Penalty Amount (KES)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (amountController.text.isNotEmpty && reasonController.text.isNotEmpty) {
                // For demo purposes, we'll use the first chama
                if (controller.chamas.isNotEmpty) {
                  controller.addGeneralPenalty(
                    chamaId: controller.chamas.first.id ?? 0,
                    amount: double.tryParse(amountController.text) ?? 0.0,
                    reason: reasonController.text,
                    description: descriptionController.text.isNotEmpty ? descriptionController.text : null,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Add Penalty'),
          ),
        ],
      ),
    );
  }

  void _showBlockChamaDialog(BuildContext context) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Chama'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for blocking this chama:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                // For demo purposes, we'll block the first chama
                if (controller.chamas.isNotEmpty) {
                  controller.blockChama(
                    controller.chamas.first.id ?? 0,
                    reasonController.text,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Block Chama'),
          ),
        ],
      ),
    );
  }

  void _showUnblockChamaDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unblock Chama'),
        content: const Text('Are you sure you want to unblock this chama?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll unblock the first chama
              if (controller.chamas.isNotEmpty) {
                controller.unblockChama(controller.chamas.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Unblock Chama'),
          ),
        ],
      ),
    );
  }
}
